<?php
/**
 * Script funcional para Trivision CR - Versión Debug
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== TRIVISION CR - SCRIPT FUNCIONAL ===" . PHP_EOL . PHP_EOL;

// URLs conocidas que funcionan
$known_urls = [
    'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/chunklist.m3u8',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/playlist.m3u8'
];

// Función para obtener contenido con método simple
function getContentSimple($url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'ignore_errors' => true
        ]
    ]);
    
    return @file_get_contents($url, false, $context);
}

// Función para construir URL del chunklist
function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

// Probar cada URL conocida
foreach ($known_urls as $index => $url) {
    echo "Probando URL #" . ($index + 1) . ": $url" . PHP_EOL;
    
    $content = getContentSimple($url);
    
    if ($content) {
        echo "✓ Contenido obtenido. Tamaño: " . strlen($content) . " bytes" . PHP_EOL;
        
        if (strpos($content, '#EXTM3U') !== false) {
            echo "✓ Es un M3U8 válido!" . PHP_EOL;
            
            // Mostrar primeras líneas del contenido
            $lines = explode("\n", $content);
            echo "Primeras líneas del M3U8:" . PHP_EOL;
            for ($i = 0; $i < min(10, count($lines)); $i++) {
                echo "  " . trim($lines[$i]) . PHP_EOL;
            }
            
            // Buscar chunklist
            if (preg_match('/chunklist(.*?)\n/', $content, $chunklist)) {
                $url_completa = construirUrlChunklist($url, trim($chunklist[0]));
                echo "✓ Chunklist encontrado: " . trim($chunklist[0]) . PHP_EOL;
                echo "✓ URL completa: $url_completa" . PHP_EOL;
                echo PHP_EOL . "🎯 STREAM ENCONTRADO: $url_completa" . PHP_EOL;
                echo "Para usar en tu reproductor, copia esta URL: $url_completa" . PHP_EOL;
                exit;
            } 
            // Si no hay chunklist pero hay segmentos .ts
            elseif (strpos($content, '.ts') !== false) {
                echo "✓ Contiene segmentos .ts directos" . PHP_EOL;
                echo PHP_EOL . "🎯 STREAM ENCONTRADO: $url" . PHP_EOL;
                echo "Para usar en tu reproductor, copia esta URL: $url" . PHP_EOL;
                exit;
            }
            // Si es un playlist master, buscar el de mejor calidad
            elseif (preg_match('/.*\.m3u8/', $content, $matches)) {
                $best_playlist = construirUrlChunklist($url, trim($matches[0]));
                echo "✓ Playlist master encontrado: " . trim($matches[0]) . PHP_EOL;
                echo "✓ URL del playlist: $best_playlist" . PHP_EOL;
                echo PHP_EOL . "🎯 STREAM ENCONTRADO: $best_playlist" . PHP_EOL;
                echo "Para usar en tu reproductor, copia esta URL: $best_playlist" . PHP_EOL;
                exit;
            } else {
                echo "ℹ M3U8 válido pero sin chunklist o segmentos reconocibles" . PHP_EOL;
                echo "Contenido completo:" . PHP_EOL;
                echo $content . PHP_EOL;
            }
        } else {
            echo "❌ No es un M3U8 válido" . PHP_EOL;
            echo "Primeros 200 caracteres:" . PHP_EOL;
            echo substr($content, 0, 200) . PHP_EOL;
        }
    } else {
        echo "❌ No se pudo obtener contenido" . PHP_EOL;
    }
    
    echo "----------------------------------------" . PHP_EOL . PHP_EOL;
}

echo "❌ No se pudo obtener ningún stream funcional" . PHP_EOL;
echo PHP_EOL . "=== PROCESO COMPLETADO ===" . PHP_EOL;
?>
