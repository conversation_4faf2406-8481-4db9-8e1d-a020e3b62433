<?php
/**
 * Script PHP para extraer y generar enlace M3U8 autoactualizable de Trivision CR
 * Basado en los datos proporcionados del HLS downloader y F12
 */

class TrivisionM3U8Extractor {
    
    private $baseUrl = 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/';
    private $playlistUrl = 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR';
    private $segmentBaseUrl = 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/';
    
    public function __construct() {
        // Configurar headers para simular un navegador
        $this->setupHeaders();
    }
    
    private function setupHeaders() {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept: */*',
                    'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
                    'Accept-Encoding: gzip, deflate, br',
                    'Connection: keep-alive',
                    'Referer: https://trivisioncr.com/',
                    'Origin: https://trivisioncr.com'
                ],
                'timeout' => 30
            ]
        ]);
        
        stream_context_set_default($context);
    }
    
    /**
     * Obtiene el contenido del playlist M3U8 original
     */
    public function getOriginalPlaylist() {
        try {
            $content = file_get_contents($this->playlistUrl);
            if ($content === false) {
                throw new Exception('No se pudo obtener el playlist original');
            }
            return $content;
        } catch (Exception $e) {
            error_log('Error obteniendo playlist: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Procesa el playlist M3U8 y actualiza las URLs de los segmentos
     */
    public function processPlaylist($content) {
        if (!$content) {
            return false;
        }
        
        $lines = explode("\n", $content);
        $processedLines = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Si es una línea de segmento (.ts)
            if (preg_match('/\.ts(\?.*)?$/', $line)) {
                // Extraer solo el nombre del archivo
                $filename = basename(parse_url($line, PHP_URL_PATH));
                
                // Construir la nueva URL completa
                $newUrl = $this->segmentBaseUrl . $filename;
                $processedLines[] = $newUrl;
            } else {
                // Mantener las líneas de metadatos como están
                $processedLines[] = $line;
            }
        }
        
        return implode("\n", $processedLines);
    }
    
    /**
     * Genera un playlist M3U8 autoactualizable
     */
    public function generateAutoUpdatingPlaylist() {
        $originalContent = $this->getOriginalPlaylist();
        
        if (!$originalContent) {
            return $this->generateErrorPlaylist();
        }
        
        $processedContent = $this->processPlaylist($originalContent);
        
        if (!$processedContent) {
            return $this->generateErrorPlaylist();
        }
        
        // Agregar headers específicos para M3U8
        $headers = [
            '#EXTM3U',
            '#EXT-X-VERSION:3',
            '#EXT-X-TARGETDURATION:10',
            '#EXT-X-MEDIA-SEQUENCE:' . time(),
            '#EXT-X-PLAYLIST-TYPE:EVENT'
        ];
        
        // Combinar headers con contenido procesado
        $lines = explode("\n", $processedContent);
        $finalContent = [];
        
        // Agregar headers personalizados
        foreach ($headers as $header) {
            if (!in_array($header, $lines)) {
                $finalContent[] = $header;
            }
        }
        
        // Agregar contenido procesado
        foreach ($lines as $line) {
            if (!empty(trim($line))) {
                $finalContent[] = $line;
            }
        }
        
        return implode("\n", $finalContent);
    }
    
    /**
     * Genera un playlist de error cuando no se puede obtener el contenido
     */
    private function generateErrorPlaylist() {
        return "#EXTM3U\n#EXT-X-VERSION:3\n#EXT-X-ENDLIST\n# Error: No se pudo obtener el stream";
    }
    
    /**
     * Obtiene información del stream actual
     */
    public function getStreamInfo() {
        $originalContent = $this->getOriginalPlaylist();
        
        if (!$originalContent) {
            return [
                'status' => 'error',
                'message' => 'No se pudo conectar al stream'
            ];
        }
        
        $lines = explode("\n", $originalContent);
        $segmentCount = 0;
        $duration = 0;
        
        foreach ($lines as $line) {
            if (strpos($line, '#EXTINF:') === 0) {
                $duration += floatval(str_replace(['#EXTINF:', ','], '', $line));
            }
            if (preg_match('/\.ts(\?.*)?$/', $line)) {
                $segmentCount++;
            }
        }
        
        return [
            'status' => 'online',
            'segments' => $segmentCount,
            'duration' => round($duration, 2),
            'last_update' => date('Y-m-d H:i:s'),
            'base_url' => $this->segmentBaseUrl
        ];
    }
}

// Configurar headers HTTP para M3U8
header('Content-Type: application/vnd.apple.mpegurl');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Manejar diferentes acciones
$action = $_GET['action'] ?? 'playlist';

$extractor = new TrivisionM3U8Extractor();

switch ($action) {
    case 'playlist':
        // Generar playlist M3U8 autoactualizable
        echo $extractor->generateAutoUpdatingPlaylist();
        break;
        
    case 'info':
        // Obtener información del stream
        header('Content-Type: application/json');
        echo json_encode($extractor->getStreamInfo(), JSON_PRETTY_PRINT);
        break;
        
    case 'original':
        // Mostrar playlist original
        echo $extractor->getOriginalPlaylist();
        break;
        
    default:
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Acción no válida',
            'available_actions' => ['playlist', 'info', 'original']
        ]);
        break;
}
?>
