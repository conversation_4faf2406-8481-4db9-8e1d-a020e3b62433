<?php
/**
 * Script PHP para extraer M3U8 de Trivision CR
 * Basado en el modelo funcional de El Salvador
 */

header('Content-Type: text/plain');

// URL de la página de TV en vivo de Trivision CR
$url = "https://trivisioncr.com/tv-en-vivo/";

// Headers para simular navegador móvil (como el modelo de El Salvador)
$headers = [
    "Host: trivisioncr.com",
    "Connection: keep-alive",
    'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
    "sec-ch-ua-mobile: ?1",
    'sec-ch-ua-platform: "Android"',
    "Upgrade-Insecure-Requests: 1",
    "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Sec-Fetch-Site: cross-site",
    "Sec-Fetch-Mode: navigate",
    "Sec-Fetch-Dest: iframe",
    "Sec-Fetch-Storage-Access: active",
    "Accept-Encoding: gzip, deflate, br, zstd",
    "Accept-Language: es-MX,es-419;q=0.9,es;q=0.8"
];

// Realizar petición con cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_ENCODING, '');
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// Verificar si la petición fue exitosa
if ($httpCode !== 200 || !$response) {
    echo "Error: No se pudo obtener la página web. Código HTTP: $httpCode" . PHP_EOL;
    exit;
}

// Buscar URL M3U8 en el HTML usando patrones más amplios
$m3u8_patterns = [
    '/https?:\/\/[^\s"\'<>]+\.m3u8(?:\?[^\s"\'<>]*)?/i',
    '/["\']([^"\']*liveingesta[^"\']*\.m3u8[^"\']*)["\']/',
    '/["\']([^"\']*trivision[^"\']*\.m3u8[^"\']*)["\']/',
    '/src\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']/',
    '/playlist\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']/'
];

$m3u8_url = null;
foreach ($m3u8_patterns as $pattern) {
    if (preg_match($pattern, $response, $matches)) {
        $m3u8_url = isset($matches[1]) ? $matches[1] : $matches[0];
        // Limpiar la URL de caracteres no deseados
        $m3u8_url = trim($m3u8_url, '"\'');
        break;
    }
}

// Si no se encontró URL M3U8, intentar con URL conocida como fallback
if (!$m3u8_url) {
    echo "No se encontró URL M3U8 en la página. Intentando con URL conocida..." . PHP_EOL;
    $m3u8_url = "https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR";
}

echo "URL M3U8 encontrada: $m3u8_url" . PHP_EOL;

// Obtener el contenido del playlist M3U8
$playlist_content = obtenerSegmentosM3U8($m3u8_url);

if (!$playlist_content) {
    echo "Error: No se pudo obtener el contenido del playlist M3U8." . PHP_EOL;
    exit;
}

// Buscar chunklist en el contenido del playlist
if (preg_match('/chunklist(.*?)\n/', $playlist_content, $chunklist)) {
    $url_completa = construirUrlChunklist($m3u8_url, trim($chunklist[0]));
    echo "URL chunklist encontrada: $url_completa" . PHP_EOL;
    header("Location: " . $url_completa);
    exit;
} else {
    // Si no hay chunklist, buscar directamente archivos .ts o devolver el playlist original
    if (strpos($playlist_content, '.ts') !== false) {
        echo "Playlist con segmentos .ts encontrado. Redirigiendo al playlist original..." . PHP_EOL;
        header("Location: " . $m3u8_url);
        exit;
    } else {
        echo "No se encontró chunklist ni segmentos .ts en el playlist." . PHP_EOL;
        echo "Contenido del playlist:" . PHP_EOL;
        echo $playlist_content;
    }
}

// Función para construir URL del chunklist (como en el modelo de El Salvador)
function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

// Función para obtener segmentos M3U8 (como en el modelo de El Salvador)
function obtenerSegmentosM3U8($m3u8_url) {
    $parsed_url = parse_url($m3u8_url);
    $host = $parsed_url['host'];
    $path = $parsed_url['path'] . (isset($parsed_url['query']) ? '?' . $parsed_url['query'] : '');

    $headers = [
        "Host: $host",
        'sec-ch-ua-platform: "Android"',
        "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        "sec-ch-ua-mobile: ?1",
        "Accept: */*",
        "Origin: https://trivisioncr.com",
        "Sec-Fetch-Site: cross-site",
        "Sec-Fetch-Mode: cors",
        "Sec-Fetch-Dest: empty",
        "Referer: https://trivisioncr.com/",
        "Accept-Encoding: gzip, deflate, br, zstd",
        "Accept-Language: es-MX,es-419;q=0.9,es;q=0.8"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $m3u8_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    curl_close($ch);
    return $response;
}

?>
