<?php
/**
 * Script para analizar la página de Trivision CR y encontrar el reproductor
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== ANÁLISIS DE PÁGINA TRIVISION CR ===" . PHP_EOL . PHP_EOL;

// Obtener el HTML de la página
$url = "https://trivisioncr.com/tv-en-vivo/";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    CURLOPT_HTTPHEADER => [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Connection: keep-alive',
        'Upgrade-Insecure-Requests: 1'
    ]
]);

$html = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code !== 200 || !$html) {
    echo "❌ Error al obtener la página. Código HTTP: $http_code" . PHP_EOL;
    exit;
}

echo "✓ Página obtenida exitosamente" . PHP_EOL;
echo "✓ Tamaño: " . strlen($html) . " bytes" . PHP_EOL . PHP_EOL;

// Buscar diferentes elementos relacionados con video
echo "=== ANÁLISIS DE ELEMENTOS DE VIDEO ===" . PHP_EOL . PHP_EOL;

// 1. Buscar iframes
echo "1. IFRAMES ENCONTRADOS:" . PHP_EOL;
if (preg_match_all('/<iframe[^>]*src\s*=\s*["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
    foreach ($matches[1] as $i => $src) {
        echo "  - $src" . PHP_EOL;
        
        // Si es un iframe externo, intentar analizarlo
        if (strpos($src, 'trivisioncr.com') === false && strpos($src, 'http') === 0) {
            echo "    (iframe externo - analizando...)" . PHP_EOL;
            analyzeExternalFrame($src);
        }
    }
} else {
    echo "  No se encontraron iframes" . PHP_EOL;
}

echo PHP_EOL;

// 2. Buscar elementos video/source
echo "2. ELEMENTOS VIDEO/SOURCE:" . PHP_EOL;
if (preg_match_all('/<(?:video|source)[^>]*src\s*=\s*["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
    foreach ($matches[1] as $src) {
        echo "  - $src" . PHP_EOL;
    }
} else {
    echo "  No se encontraron elementos video/source" . PHP_EOL;
}

echo PHP_EOL;

// 3. Buscar scripts que puedan contener configuración del reproductor
echo "3. SCRIPTS CON POSIBLE CONFIGURACIÓN:" . PHP_EOL;
if (preg_match_all('/<script[^>]*>(.*?)<\/script>/is', $html, $matches)) {
    $script_count = 0;
    foreach ($matches[1] as $script_content) {
        // Buscar patrones relacionados con streaming
        if (preg_match('/(m3u8|hls|stream|player|video)/i', $script_content)) {
            $script_count++;
            echo "  Script #$script_count con contenido de streaming:" . PHP_EOL;
            
            // Extraer URLs potenciales
            if (preg_match_all('/["\']([^"\']*(?:m3u8|stream|live)[^"\']*)["\']/', $script_content, $url_matches)) {
                foreach ($url_matches[1] as $potential_url) {
                    if (strlen($potential_url) > 10) {
                        echo "    URL potencial: $potential_url" . PHP_EOL;
                    }
                }
            }
            
            // Mostrar fragmento del script
            $fragment = substr(trim($script_content), 0, 300);
            echo "    Fragmento: " . $fragment . "..." . PHP_EOL . PHP_EOL;
        }
    }
    
    if ($script_count === 0) {
        echo "  No se encontraron scripts con configuración de streaming" . PHP_EOL;
    }
} else {
    echo "  No se encontraron scripts" . PHP_EOL;
}

echo PHP_EOL;

// 4. Buscar URLs en general que puedan ser relevantes
echo "4. URLs RELEVANTES ENCONTRADAS:" . PHP_EOL;
$patterns = [
    'M3U8' => '/https?:\/\/[^\s"\'<>]+\.m3u8[^\s"\'<>]*/i',
    'Streaming domains' => '/https?:\/\/[^\s"\'<>]*(?:stream|live|cdn|media)[^\s"\'<>]*/i',
    'Video files' => '/https?:\/\/[^\s"\'<>]+\.(?:mp4|m4v|webm|ts)[^\s"\'<>]*/i'
];

foreach ($patterns as $name => $pattern) {
    echo "  $name:" . PHP_EOL;
    if (preg_match_all($pattern, $html, $matches)) {
        $unique_urls = array_unique($matches[0]);
        foreach ($unique_urls as $url) {
            if (strlen($url) > 20) { // Filtrar URLs muy cortas
                echo "    - $url" . PHP_EOL;
            }
        }
    } else {
        echo "    No encontradas" . PHP_EOL;
    }
    echo PHP_EOL;
}

// 5. Buscar divs o elementos con IDs/clases relacionadas con video
echo "5. ELEMENTOS CON IDS/CLASES DE VIDEO:" . PHP_EOL;
if (preg_match_all('/<[^>]*(?:id|class)\s*=\s*["\'][^"\']*(?:video|player|stream|live)[^"\']*["\'][^>]*>/i', $html, $matches)) {
    foreach (array_unique($matches[0]) as $element) {
        echo "  - " . substr($element, 0, 100) . "..." . PHP_EOL;
    }
} else {
    echo "  No se encontraron elementos con IDs/clases de video" . PHP_EOL;
}

echo PHP_EOL . "=== ANÁLISIS COMPLETADO ===" . PHP_EOL;

// Función para analizar iframes externos
function analyzeExternalFrame($iframe_url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $iframe_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Referer: https://trivisioncr.com/'
        ]
    ]);
    
    $iframe_content = curl_exec($ch);
    $iframe_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($iframe_http_code === 200 && $iframe_content) {
        echo "      ✓ Iframe accesible" . PHP_EOL;
        
        // Buscar M3U8 en el iframe
        if (preg_match_all('/https?:\/\/[^\s"\'<>]+\.m3u8[^\s"\'<>]*/i', $iframe_content, $matches)) {
            foreach ($matches[0] as $m3u8_url) {
                echo "      ✓ M3U8 encontrado: $m3u8_url" . PHP_EOL;
            }
        }
    } else {
        echo "      ❌ Iframe no accesible. Código: $iframe_http_code" . PHP_EOL;
    }
}

?>
