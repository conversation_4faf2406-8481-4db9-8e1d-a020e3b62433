<?php
/**
 * Script simple para extraer M3U8 de Trivision CR
 * Basado exactamente en el modelo funcional de El Salvador
 */

header('Content-Type: text/plain');

// URL de la página de TV en vivo de Trivision CR
$url = "https://trivisioncr.com/tv-en-vivo/";

$headers = [
    "Host: trivisioncr.com",
    "Connection: keep-alive",
    'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
    "sec-ch-ua-mobile: ?1",
    'sec-ch-ua-platform: "Android"',
    "Upgrade-Insecure-Requests: 1",
    "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Sec-Fetch-Site: cross-site",
    "Sec-Fetch-Mode: navigate",
    "Sec-Fetch-Dest: iframe",
    "Sec-Fetch-Storage-Access: active",
    "Accept-Encoding: gzip, deflate, br, zstd",
    "Accept-Language: es-MX,es-419;q=0.9,es;q=0.8"
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_ENCODING, ''); 
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
curl_close($ch);

// Buscar URL M3U8 en la respuesta
if (preg_match('/https?:\/\/[^\s"\']+\.m3u8(?:\?[^\s"\']*)?/', $response, $matches)) {
    echo "URL M3U8 encontrada: " . $matches[0] . PHP_EOL;
    
    // Obtener el contenido del playlist M3U8
    $playlist_content = obtenerSegmentosM3U8($matches[0]);
    
    if ($playlist_content) {
        echo "Contenido del playlist obtenido exitosamente." . PHP_EOL;
        
        // Buscar chunklist en el contenido
        if (preg_match('/chunklist(.*?)\n/', $playlist_content, $chunklist)) {
            $url_completa = construirUrlChunklist($matches[0], trim($chunklist[0]));
            echo "URL chunklist: " . $url_completa . PHP_EOL;
            header("Location: " . $url_completa); 
            exit;
        } else {
            echo "No se encontró chunklist. Contenido del playlist:" . PHP_EOL;
            echo $playlist_content . PHP_EOL;
            
            // Si no hay chunklist pero hay segmentos .ts, redirigir al playlist original
            if (strpos($playlist_content, '.ts') !== false) {
                echo "Redirigiendo al playlist original con segmentos .ts" . PHP_EOL;
                header("Location: " . $matches[0]);
                exit;
            }
        }
    } else {
        echo "Error: No se pudo obtener el contenido del playlist M3U8." . PHP_EOL;
    }
} else {
    echo "No se encontró ninguna URL M3U8 en la página." . PHP_EOL;
    echo "Intentando con URL conocida..." . PHP_EOL;
    
    // Fallback a URL conocida
    $fallback_url = "https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR";
    $playlist_content = obtenerSegmentosM3U8($fallback_url);
    
    if ($playlist_content) {
        echo "Playlist obtenido con URL de fallback." . PHP_EOL;
        
        if (preg_match('/chunklist(.*?)\n/', $playlist_content, $chunklist)) {
            $url_completa = construirUrlChunklist($fallback_url, trim($chunklist[0]));
            echo "URL chunklist (fallback): " . $url_completa . PHP_EOL;
            header("Location: " . $url_completa); 
            exit;
        } else {
            echo "Contenido del playlist (fallback):" . PHP_EOL;
            echo $playlist_content . PHP_EOL;
            
            if (strpos($playlist_content, '.ts') !== false) {
                header("Location: " . $fallback_url);
                exit;
            }
        }
    } else {
        echo "Error: No se pudo obtener el playlist con URL de fallback." . PHP_EOL;
    }
}

function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

function obtenerSegmentosM3U8($m3u8_url) {
    $parsed_url = parse_url($m3u8_url);
    $host = $parsed_url['host'];
    $path = $parsed_url['path'] . (isset($parsed_url['query']) ? '?' . $parsed_url['query'] : '');

    $headers = [
        "Host: $host",
        'sec-ch-ua-platform: "Android"',
        "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        "sec-ch-ua-mobile: ?1",
        "Accept: */*",
        "Origin: https://trivisioncr.com",
        "Sec-Fetch-Site: cross-site",
        "Sec-Fetch-Mode: cors",
        "Sec-Fetch-Dest: empty",
        "Referer: https://trivisioncr.com/",
        "Accept-Encoding: gzip, deflate, br, zstd",
        "Accept-Language: es-MX,es-419;q=0.9,es;q=0.8"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $m3u8_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    
    if (curl_error($ch)) {
        echo "Error cURL: " . curl_error($ch) . PHP_EOL;
    }
    
    curl_close($ch);
    return $response;
}

?>
