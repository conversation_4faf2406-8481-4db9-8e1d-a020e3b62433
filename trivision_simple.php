<?php
/**
 * Script simple para extraer M3U8 de Trivision CR
 * Basado exactamente en el modelo funcional de El Salvador
 */

header('Content-Type: text/plain');

// Primero intentar directamente con la URL conocida del M3U8
echo "Intentando con URL conocida del M3U8..." . PHP_EOL;
$fallback_url = "https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR";

$playlist_content = obtenerSegmentosM3U8($fallback_url);

if ($playlist_content && strpos($playlist_content, '#EXTM3U') !== false) {
    echo "✓ Playlist M3U8 obtenido exitosamente!" . PHP_EOL;
    echo "Tamaño: " . strlen($playlist_content) . " bytes" . PHP_EOL;

    // Buscar chunklist en el contenido
    if (preg_match('/chunklist(.*?)\n/', $playlist_content, $chunklist)) {
        $url_completa = construirUrlChunklist($fallback_url, trim($chunklist[0]));
        echo "✓ URL chunklist encontrada: " . $url_completa . PHP_EOL;
        header("Location: " . $url_completa);
        exit;
    } else {
        echo "ℹ No se encontró chunklist específico." . PHP_EOL;

        // Verificar si hay segmentos .ts directamente
        if (strpos($playlist_content, '.ts') !== false) {
            echo "✓ Playlist contiene segmentos .ts. Redirigiendo al playlist original..." . PHP_EOL;
            header("Location: " . $fallback_url);
            exit;
        } else {
            echo "📄 Contenido del playlist:" . PHP_EOL;
            echo $playlist_content . PHP_EOL;
        }
    }
} else {
    echo "❌ No se pudo obtener el playlist con URL conocida." . PHP_EOL;
    echo "Intentando analizar la página web..." . PHP_EOL;

    // Si falla, intentar obtener de la página web
    $url = "https://trivisioncr.com/tv-en-vivo/";

    $headers = [
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language: es-ES,es;q=0.5",
        "Accept-Encoding: gzip, deflate, br",
        "Connection: keep-alive",
        "Upgrade-Insecure-Requests: 1"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Código HTTP: $httpCode" . PHP_EOL;

    if ($httpCode == 200 && $response) {
        // Buscar URL M3U8 en la respuesta
        if (preg_match('/https?:\/\/[^\s"\']+\.m3u8(?:\?[^\s"\']*)?/', $response, $matches)) {
            echo "✓ URL M3U8 encontrada en página: " . $matches[0] . PHP_EOL;

            // Obtener el contenido del playlist M3U8
            $playlist_content = obtenerSegmentosM3U8($matches[0]);

            if ($playlist_content) {
                echo "✓ Contenido del playlist obtenido exitosamente." . PHP_EOL;

                // Buscar chunklist en el contenido
                if (preg_match('/chunklist(.*?)\n/', $playlist_content, $chunklist)) {
                    $url_completa = construirUrlChunklist($matches[0], trim($chunklist[0]));
                    echo "✓ URL chunklist: " . $url_completa . PHP_EOL;
                    header("Location: " . $url_completa);
                    exit;
                } else {
                    echo "ℹ No se encontró chunklist. Contenido del playlist:" . PHP_EOL;
                    echo $playlist_content . PHP_EOL;

                    // Si no hay chunklist pero hay segmentos .ts, redirigir al playlist original
                    if (strpos($playlist_content, '.ts') !== false) {
                        echo "✓ Redirigiendo al playlist original con segmentos .ts" . PHP_EOL;
                        header("Location: " . $matches[0]);
                        exit;
                    }
                }
            } else {
                echo "❌ Error: No se pudo obtener el contenido del playlist M3U8." . PHP_EOL;
            }
        } else {
            echo "❌ No se encontró ninguna URL M3U8 en la página." . PHP_EOL;
        }
    } else {
        echo "❌ Error al acceder a la página. Código HTTP: $httpCode" . PHP_EOL;
    }
}

function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

function obtenerSegmentosM3U8($m3u8_url) {
    $parsed_url = parse_url($m3u8_url);
    $host = $parsed_url['host'];
    $path = $parsed_url['path'] . (isset($parsed_url['query']) ? '?' . $parsed_url['query'] : '');

    $headers = [
        "Host: $host",
        'sec-ch-ua-platform: "Android"',
        "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        "sec-ch-ua-mobile: ?1",
        "Accept: */*",
        "Origin: https://trivisioncr.com",
        "Sec-Fetch-Site: cross-site",
        "Sec-Fetch-Mode: cors",
        "Sec-Fetch-Dest: empty",
        "Referer: https://trivisioncr.com/",
        "Accept-Encoding: gzip, deflate, br, zstd",
        "Accept-Language: es-MX,es-419;q=0.9,es;q=0.8"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $m3u8_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    
    if (curl_error($ch)) {
        echo "Error cURL: " . curl_error($ch) . PHP_EOL;
    }
    
    curl_close($ch);
    return $response;
}

?>
