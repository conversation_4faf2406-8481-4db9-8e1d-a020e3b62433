<?php
/**
 * Proxy para segmentos de Trivision CR
 * Intenta obtener segmentos .ts y servirlos
 */

// Configurar headers para streaming
header('Content-Type: video/mp2t');
header('Access-Control-Allow-Origin: *');
header('Cache-Control: no-cache');

$segment = $_GET['segment'] ?? time();

// URLs base conocidas para segmentos
$segment_bases = [
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1000/',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-500/'
];

// Generar nombres de segmento basados en el patrón conocido
$segment_patterns = [
    "media-" . substr(md5($segment), 0, 8) . "_DVR_" . $segment . ".ts",
    "media-" . substr(md5($segment . "alt"), 0, 8) . "_" . $segment . ".ts",
    "segment_" . $segment . ".ts"
];

// Función para intentar obtener segmento
function getSegment($url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'VLC/3.0.16 LibVLC/3.0.16'
    ]);
    
    $content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    if ($http_code == 200 && $content && strpos($content_type, 'video') !== false) {
        return $content;
    }
    
    return false;
}

// Probar diferentes combinaciones
foreach ($segment_bases as $base) {
    foreach ($segment_patterns as $pattern) {
        $segment_url = $base . $pattern;
        $content = getSegment($segment_url);
        
        if ($content) {
            echo $content;
            exit;
        }
    }
}

// Si no se encuentra segmento real, generar contenido de placeholder
header('Content-Type: text/plain');
echo "Segment not available - Server blocking access";
?>
