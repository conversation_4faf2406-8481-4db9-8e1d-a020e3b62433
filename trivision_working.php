<?php
/**
 * Script funcional para Trivision CR
 * Basado en el descubrimiento de que funciona con peticiones simples
 */

// URLs conocidas que funcionan
$known_urls = [
    'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/chunklist.m3u8',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/playlist.m3u8'
];

// Función para obtener contenido con método simple
function getContentSimple($url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'ignore_errors' => true
        ]
    ]);
    
    return @file_get_contents($url, false, $context);
}

// Función para construir URL del chunklist
function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

// Probar cada URL conocida
foreach ($known_urls as $url) {
    $content = getContentSimple($url);
    
    if ($content && strpos($content, '#EXTM3U') !== false) {
        // Es un M3U8 válido
        
        // Buscar chunklist
        if (preg_match('/chunklist(.*?)\n/', $content, $chunklist)) {
            $url_completa = construirUrlChunklist($url, trim($chunklist[0]));
            header("Location: " . $url_completa);
            exit;
        } 
        // Si no hay chunklist pero hay segmentos .ts
        elseif (strpos($content, '.ts') !== false) {
            header("Location: " . $url);
            exit;
        }
        // Si es un playlist master, buscar el de mejor calidad
        elseif (preg_match('/.*\.m3u8/', $content, $matches)) {
            $best_playlist = construirUrlChunklist($url, trim($matches[0]));
            header("Location: " . $best_playlist);
            exit;
        }
    }
}

// Si llegamos aquí, no se pudo obtener ningún stream
header('Content-Type: text/plain');
echo "Error: No se pudo obtener el stream de Trivision CR";
?>
