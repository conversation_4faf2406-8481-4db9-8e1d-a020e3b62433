<?php
/**
 * Proxy para segmentos .ts de Trivision CR
 * Permite servir los segmentos con headers apropiados y manejo de errores
 */

class SegmentProxy {
    
    private $baseUrl = 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/';
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    private $maxFileSize = 50 * 1024 * 1024; // 50MB máximo
    
    /**
     * Valida el nombre del archivo solicitado
     */
    private function validateFilename($filename) {
        // Solo permitir archivos .ts con formato específico
        if (!preg_match('/^media-[a-zA-Z0-9_]+_DVR_\d+\.ts$/', $filename)) {
            return false;
        }
        
        // Verificar que no contenga caracteres peligrosos
        if (strpos($filename, '..') !== false || 
            strpos($filename, '/') !== false || 
            strpos($filename, '\\') !== false) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Obtiene el segmento del servidor original
     */
    public function getSegment($filename) {
        if (!$this->validateFilename($filename)) {
            throw new Exception('Nombre de archivo no válido');
        }
        
        $url = $this->baseUrl . $filename;
        
        // Configurar contexto con headers apropiados
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: ' . $this->userAgent,
                    'Accept: */*',
                    'Accept-Encoding: gzip, deflate, br',
                    'Connection: keep-alive',
                    'Referer: https://trivisioncr.com/',
                    'Origin: https://trivisioncr.com'
                ],
                'timeout' => 30
            ]
        ]);
        
        // Obtener el contenido
        $content = file_get_contents($url, false, $context);
        
        if ($content === false) {
            throw new Exception('No se pudo obtener el segmento');
        }
        
        // Verificar tamaño
        if (strlen($content) > $this->maxFileSize) {
            throw new Exception('Archivo demasiado grande');
        }
        
        // Verificar que sea un archivo TS válido
        if (substr($content, 0, 1) !== "\x47") { // Los archivos TS empiezan con 0x47
            throw new Exception('Archivo TS no válido');
        }
        
        return $content;
    }
    
    /**
     * Sirve el segmento con headers apropiados
     */
    public function serveSegment($filename) {
        try {
            $content = $this->getSegment($filename);
            
            // Headers para archivo TS
            header('Content-Type: video/mp2t');
            header('Content-Length: ' . strlen($content));
            header('Accept-Ranges: bytes');
            header('Cache-Control: public, max-age=300'); // Cache por 5 minutos
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, HEAD, OPTIONS');
            header('Access-Control-Allow-Headers: Range');
            
            // Manejar solicitudes de rango (para streaming)
            if (isset($_SERVER['HTTP_RANGE'])) {
                $this->handleRangeRequest($content);
            } else {
                echo $content;
            }
            
        } catch (Exception $e) {
            $this->serveError($e->getMessage());
        }
    }
    
    /**
     * Maneja solicitudes de rango HTTP
     */
    private function handleRangeRequest($content) {
        $size = strlen($content);
        $range = $_SERVER['HTTP_RANGE'];
        
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = $matches[2] ? intval($matches[2]) : $size - 1;
            
            if ($start >= $size || $end >= $size || $start > $end) {
                header('HTTP/1.1 416 Requested Range Not Satisfiable');
                header('Content-Range: bytes */' . $size);
                return;
            }
            
            $length = $end - $start + 1;
            
            header('HTTP/1.1 206 Partial Content');
            header('Content-Range: bytes ' . $start . '-' . $end . '/' . $size);
            header('Content-Length: ' . $length);
            
            echo substr($content, $start, $length);
        } else {
            echo $content;
        }
    }
    
    /**
     * Sirve un error con formato apropiado
     */
    private function serveError($message) {
        http_response_code(404);
        header('Content-Type: text/plain');
        echo 'Error: ' . $message;
        error_log('Segment Proxy Error: ' . $message);
    }
    
    /**
     * Obtiene información sobre un segmento sin descargarlo completamente
     */
    public function getSegmentInfo($filename) {
        if (!$this->validateFilename($filename)) {
            return ['error' => 'Nombre de archivo no válido'];
        }
        
        $url = $this->baseUrl . $filename;
        
        // Usar HEAD request para obtener solo headers
        $context = stream_context_create([
            'http' => [
                'method' => 'HEAD',
                'header' => [
                    'User-Agent: ' . $this->userAgent,
                    'Referer: https://trivisioncr.com/'
                ],
                'timeout' => 10
            ]
        ]);
        
        $headers = get_headers($url, 1, $context);
        
        if (!$headers) {
            return ['error' => 'No se pudieron obtener los headers'];
        }
        
        return [
            'filename' => $filename,
            'url' => $url,
            'status' => $headers[0] ?? 'Unknown',
            'content_length' => $headers['Content-Length'] ?? 'Unknown',
            'content_type' => $headers['Content-Type'] ?? 'Unknown',
            'last_modified' => $headers['Last-Modified'] ?? 'Unknown',
            'server' => $headers['Server'] ?? 'Unknown'
        ];
    }
}

// Procesar la solicitud
$filename = $_GET['file'] ?? '';
$action = $_GET['action'] ?? 'serve';

if (empty($filename)) {
    http_response_code(400);
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Parámetro file requerido',
        'usage' => 'segment_proxy.php?file=media-xxxxx_DVR_xxxxx.ts',
        'actions' => ['serve', 'info']
    ]);
    exit;
}

$proxy = new SegmentProxy();

switch ($action) {
    case 'serve':
        $proxy->serveSegment($filename);
        break;
        
    case 'info':
        header('Content-Type: application/json');
        echo json_encode($proxy->getSegmentInfo($filename), JSON_PRETTY_PRINT);
        break;
        
    default:
        http_response_code(400);
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Acción no válida']);
        break;
}
?>
