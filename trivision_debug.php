<?php
/**
 * Script de debug para Trivision CR
 * Ayuda a identificar las URLs y estructura del stream
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Trivision CR - Debug Tool</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .url { background: #f0f0f0; padding: 10px; word-break: break-all; }
        .content { background: #f9f9f9; padding: 10px; max-height: 300px; overflow-y: auto; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Trivision CR - Debug Tool</h1>
    
    <?php
    $action = $_GET['action'] ?? 'analyze';
    
    switch ($action) {
        case 'analyze':
            analyzeWebpage();
            break;
        case 'test_m3u8':
            testM3U8();
            break;
        case 'test_known':
            testKnownUrls();
            break;
        default:
            showMenu();
            break;
    }
    
    function showMenu() {
        echo '<div class="section">';
        echo '<h2>Opciones de Debug</h2>';
        echo '<ul>';
        echo '<li><a href="?action=analyze">Analizar página web</a></li>';
        echo '<li><a href="?action=test_m3u8">Probar extracción M3U8</a></li>';
        echo '<li><a href="?action=test_known">Probar URLs conocidas</a></li>';
        echo '</ul>';
        echo '</div>';
    }
    
    function analyzeWebpage() {
        echo '<div class="section">';
        echo '<h2>Análisis de la página web</h2>';
        
        $url = "https://trivisioncr.com/tv-en-vivo/";
        echo '<p class="info">Analizando: ' . $url . '</p>';
        
        $headers = [
            "Host: trivisioncr.com",
            "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language: es-MX,es-419;q=0.9,es;q=0.8"
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo '<p>Código HTTP: <span class="' . ($httpCode == 200 ? 'success' : 'error') . '">' . $httpCode . '</span></p>';
        echo '<p>Tamaño de respuesta: ' . strlen($response) . ' bytes</p>';
        
        if ($response) {
            // Buscar diferentes patrones de URLs M3U8
            $patterns = [
                'General M3U8' => '/https?:\/\/[^\s"\'<>]+\.m3u8(?:\?[^\s"\'<>]*)?/i',
                'Liveingesta' => '/https?:\/\/[^\s"\'<>]*liveingesta[^\s"\'<>]*\.m3u8[^\s"\'<>]*/i',
                'Trivision' => '/https?:\/\/[^\s"\'<>]*trivision[^\s"\'<>]*\.m3u8[^\s"\'<>]*/i',
                'Quoted URLs' => '/["\']([^"\']*\.m3u8[^"\']*)["\']/',
                'Src attribute' => '/src\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']/',
                'Playlist property' => '/playlist\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']/'
            ];
            
            echo '<h3>URLs M3U8 encontradas:</h3>';
            $found_any = false;
            
            foreach ($patterns as $name => $pattern) {
                if (preg_match_all($pattern, $response, $matches)) {
                    echo '<h4>' . $name . ':</h4>';
                    $urls = isset($matches[1]) ? $matches[1] : $matches[0];
                    foreach (array_unique($urls) as $url) {
                        echo '<div class="url">' . htmlspecialchars(trim($url, '"\'')) . '</div>';
                        $found_any = true;
                    }
                }
            }
            
            if (!$found_any) {
                echo '<p class="error">No se encontraron URLs M3U8</p>';
            }
            
            // Buscar también iframes o elementos de video
            echo '<h3>Elementos de video/iframe:</h3>';
            if (preg_match_all('/<(?:iframe|video|source)[^>]*src\s*=\s*["\']([^"\']+)["\'][^>]*>/i', $response, $matches)) {
                foreach ($matches[1] as $src) {
                    echo '<div class="url">' . htmlspecialchars($src) . '</div>';
                }
            } else {
                echo '<p>No se encontraron elementos de video/iframe</p>';
            }
            
            // Mostrar una muestra del HTML
            echo '<h3>Muestra del HTML (primeros 2000 caracteres):</h3>';
            echo '<div class="content"><pre>' . htmlspecialchars(substr($response, 0, 2000)) . '</pre></div>';
        } else {
            echo '<p class="error">No se pudo obtener el contenido de la página</p>';
        }
        
        echo '</div>';
    }
    
    function testM3U8() {
        echo '<div class="section">';
        echo '<h2>Prueba de extracción M3U8</h2>';
        
        // Ejecutar el script simple y capturar la salida
        ob_start();
        include 'trivision_simple.php';
        $output = ob_get_clean();
        
        echo '<h3>Salida del script:</h3>';
        echo '<div class="content"><pre>' . htmlspecialchars($output) . '</pre></div>';
        
        echo '</div>';
    }
    
    function testKnownUrls() {
        echo '<div class="section">';
        echo '<h2>Prueba de URLs conocidas</h2>';
        
        $known_urls = [
            'Playlist principal' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR',
            'Base SMIL' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/',
            'Segmento base' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/'
        ];
        
        foreach ($known_urls as $name => $url) {
            echo '<h3>' . $name . '</h3>';
            echo '<div class="url">' . $url . '</div>';
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36',
                'Referer: https://trivisioncr.com/'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            echo '<p>Código HTTP: <span class="' . ($httpCode == 200 ? 'success' : 'error') . '">' . $httpCode . '</span></p>';
            
            if ($error) {
                echo '<p class="error">Error cURL: ' . $error . '</p>';
            }
            
            if ($response) {
                echo '<p>Tamaño: ' . strlen($response) . ' bytes</p>';
                echo '<div class="content"><pre>' . htmlspecialchars(substr($response, 0, 1000)) . '</pre></div>';
            }
        }
        
        echo '</div>';
    }
    ?>
    
    <div class="section">
        <h2>Enlaces útiles</h2>
        <ul>
            <li><a href="trivision_simple.php" target="_blank">Script simple</a></li>
            <li><a href="trivision_m3u8_extractor.php" target="_blank">Script extractor</a></li>
            <li><a href="trivision_advanced_extractor.php?action=test" target="_blank">Script avanzado</a></li>
        </ul>
    </div>
</body>
</html>
