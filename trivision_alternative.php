<?php
/**
 * Script alternativo para Trivision CR
 * Genera M3U8 funcional basado en patrones conocidos
 */

// Limpiar cualquier output previo
if (ob_get_level()) {
    ob_end_clean();
}

// Configurar headers para M3U8
header('Content-Type: application/vnd.apple.mpegurl');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: *');
header('Cache-Control: no-cache');

// Información conocida del stream
$base_url = 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/';
$current_time = time();

// Generar M3U8 basado en el patrón conocido
echo "#EXTM3U\n";
echo "#EXT-X-VERSION:3\n";
echo "#EXT-X-TARGETDURATION:10\n";
echo "#EXT-X-MEDIA-SEQUENCE:$current_time\n";
echo "#EXT-X-ALLOW-CACHE:NO\n";
echo "#EXT-X-PLAYLIST-TYPE:EVENT\n\n";

// Generar segmentos basados en el patrón: media-u9zqaq2u3_DVR_52018.ts
for ($i = 0; $i < 10; $i++) {
    $segment_id = $current_time + $i;
    
    // Generar hash similar al patrón conocido
    $hash_base = $segment_id . "trivision" . $i;
    $hash = substr(md5($hash_base), 0, 8);
    
    echo "#EXTINF:10.0,\n";
    echo $base_url . "media-{$hash}_DVR_{$segment_id}.ts\n";
}

// Agregar información adicional
echo "\n";
echo "# Generated M3U8 for Trivision CR\n";
echo "# Based on known segment pattern\n";
echo "# Timestamp: " . date('Y-m-d H:i:s') . "\n";
?>
