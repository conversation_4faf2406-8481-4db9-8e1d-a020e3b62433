<?php
/**
 * Script de prueba para URLs conocidas de Trivision CR
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== PRUEBA DE URLs CONOCIDAS DE TRIVISION CR ===" . PHP_EOL . PHP_EOL;

// URLs conocidas para probar
$urls_to_test = [
    'Playlist principal' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR',
    'Playlist sin DVR' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8',
    'Base SMIL' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/',
    'Chunklist directo' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/chunklist.m3u8',
    'Segmento ejemplo' => 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/media-u9zqaq2u3_DVR_52018.ts'
];

foreach ($urls_to_test as $name => $url) {
    echo "--- Probando: $name ---" . PHP_EOL;
    echo "URL: $url" . PHP_EOL;
    
    $result = testUrl($url);
    
    echo "Estado: " . ($result['success'] ? '✓ ÉXITO' : '❌ ERROR') . PHP_EOL;
    echo "Código HTTP: " . $result['http_code'] . PHP_EOL;
    echo "Tamaño: " . $result['size'] . " bytes" . PHP_EOL;
    
    if ($result['error']) {
        echo "Error cURL: " . $result['error'] . PHP_EOL;
    }
    
    if ($result['content']) {
        echo "Primeros 500 caracteres:" . PHP_EOL;
        echo substr($result['content'], 0, 500) . PHP_EOL;
        
        // Análisis específico para M3U8
        if (strpos($result['content'], '#EXTM3U') !== false) {
            echo "✓ Es un archivo M3U8 válido" . PHP_EOL;
            
            // Contar segmentos
            $segments = preg_match_all('/\.ts/', $result['content']);
            echo "Segmentos .ts encontrados: $segments" . PHP_EOL;
            
            // Buscar chunklist
            if (preg_match('/chunklist.*\.m3u8/', $result['content'], $matches)) {
                echo "✓ Chunklist encontrado: " . $matches[0] . PHP_EOL;
            }
        }
    }
    
    echo PHP_EOL . "----------------------------------------" . PHP_EOL . PHP_EOL;
}

// Función para probar una URL
function testUrl($url) {
    $ch = curl_init();
    
    // Headers que simulan un navegador real
    $headers = [
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept: */*',
        'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Connection: keep-alive',
        'Referer: https://trivisioncr.com/',
        'Origin: https://trivisioncr.com'
    ];
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]);
    
    $content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'success' => ($http_code == 200 && $content !== false),
        'http_code' => $http_code,
        'content' => $content,
        'size' => $content ? strlen($content) : 0,
        'error' => $error
    ];
}

echo "=== PRUEBA COMPLETADA ===" . PHP_EOL;
?>
