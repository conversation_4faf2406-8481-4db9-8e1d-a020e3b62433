<?php
/**
 * <PERSON>ript para Trivision CR con técnicas de bypass
 * Intenta diferentes métodos para evitar el bloqueo 403
 */

// Configurar headers para evitar output antes de redirección
ob_start();

// URLs a probar
$urls = [
    'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8',
    'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/chunklist.m3u8'
];

// Función para probar URL con diferentes técnicas
function testUrlWithBypass($url) {
    // Técnica 1: cURL con headers mínimos
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => '',  // Sin User-Agent
        CURLOPT_HTTPHEADER => []  // Sin headers adicionales
    ]);
    
    $content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200 && $content && strpos($content, '#EXTM3U') !== false) {
        return ['success' => true, 'content' => $content, 'method' => 'curl_minimal'];
    }
    
    // Técnica 2: cURL con User-Agent de reproductor
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'VLC/3.0.16 LibVLC/3.0.16'
    ]);
    
    $content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200 && $content && strpos($content, '#EXTM3U') !== false) {
        return ['success' => true, 'content' => $content, 'method' => 'curl_vlc'];
    }
    
    // Técnica 3: file_get_contents con contexto personalizado
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'ignore_errors' => true,
            'user_agent' => 'Mozilla/5.0'
        ]
    ]);
    
    $content = @file_get_contents($url, false, $context);
    if ($content && strpos($content, '#EXTM3U') !== false) {
        return ['success' => true, 'content' => $content, 'method' => 'file_get_contents'];
    }
    
    // Técnica 4: Proxy a través de otro servicio (simulado)
    // En un entorno real, aquí usarías un proxy
    
    return ['success' => false, 'content' => null, 'method' => 'none'];
}

// Función para construir URL del chunklist
function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

// Probar cada URL
foreach ($urls as $url) {
    $result = testUrlWithBypass($url);
    
    if ($result['success']) {
        $content = $result['content'];
        
        // Buscar chunklist
        if (preg_match('/chunklist.*\.m3u8/', $content, $matches)) {
            $chunklist_url = construirUrlChunklist($url, trim($matches[0]));
            
            // Limpiar buffer y redirigir
            ob_clean();
            header("Location: " . $chunklist_url);
            exit;
        }
        // Si contiene segmentos .ts directamente
        elseif (strpos($content, '.ts') !== false) {
            ob_clean();
            header("Location: " . $url);
            exit;
        }
        // Si es un playlist master
        elseif (preg_match('/.*\.m3u8/', $content, $matches)) {
            $playlist_url = construirUrlChunklist($url, trim($matches[0]));
            ob_clean();
            header("Location: " . $playlist_url);
            exit;
        }
    }
}

// Si no funciona ninguna URL, generar M3U8 proxy
ob_clean();
header('Content-Type: application/vnd.apple.mpegurl');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: *');

// Generar M3U8 que use este mismo script como proxy
$base_url = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$proxy_url = $base_url . '/trivision_segment_proxy.php';

echo "#EXTM3U\n";
echo "#EXT-X-VERSION:3\n";
echo "#EXT-X-TARGETDURATION:10\n";
echo "#EXT-X-MEDIA-SEQUENCE:" . time() . "\n";
echo "#EXT-X-ALLOW-CACHE:NO\n\n";

// Generar algunos segmentos usando el proxy
for ($i = 0; $i < 3; $i++) {
    $segment_time = time() + $i;
    echo "#EXTINF:10.0,\n";
    echo $proxy_url . "?segment=" . $segment_time . "\n";
}

echo "\n# Fallback playlist - Server may be blocking direct access\n";
echo "# Try using a VPN or different server\n";
?>
