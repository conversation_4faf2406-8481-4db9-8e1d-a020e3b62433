<?php
/**
 * Script PHP Avanzado para extraer M3U8 de Trivision CR
 * Incluye detección automática de URLs y proxy para segmentos
 */

class TrivisionAdvancedExtractor {
    
    private $trivisionUrl = 'https://trivisioncr.com/tv-en-vivo/';
    private $knownPlaylistUrl = 'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR';
    private $knownSegmentBase = 'https://liveingesta318.cdnmedia.tv/trivisionlive/rtmp01-1500/';
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    
    /**
     * Detecta automáticamente las URLs del stream desde la página web
     */
    public function detectStreamUrls() {
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: ' . $this->userAgent,
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language: es-ES,es;q=0.5',
                        'Accept-Encoding: gzip, deflate, br',
                        'Connection: keep-alive',
                        'Upgrade-Insecure-Requests: 1'
                    ],
                    'timeout' => 30
                ]
            ]);
            
            $html = file_get_contents($this->trivisionUrl, false, $context);
            
            if ($html === false) {
                throw new Exception('No se pudo obtener la página web');
            }
            
            // Buscar URLs M3U8 en el HTML
            $m3u8Urls = [];
            
            // Patrones para buscar URLs M3U8
            $patterns = [
                '/https?:\/\/[^"\s]+\.m3u8[^"\s]*/i',
                '/["\']([^"\']*\.m3u8[^"\']*)["\']/',
                '/src\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']/',
                '/playlist\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']/'
            ];
            
            foreach ($patterns as $pattern) {
                if (preg_match_all($pattern, $html, $matches)) {
                    foreach ($matches[1] ?? $matches[0] as $match) {
                        if (strpos($match, '.m3u8') !== false) {
                            $m3u8Urls[] = $match;
                        }
                    }
                }
            }
            
            // Buscar también URLs de segmentos .ts
            $tsUrls = [];
            if (preg_match_all('/https?:\/\/[^"\s]+\.ts[^"\s]*/i', $html, $matches)) {
                $tsUrls = array_unique($matches[0]);
            }
            
            return [
                'm3u8_urls' => array_unique($m3u8Urls),
                'ts_urls' => array_slice($tsUrls, 0, 5), // Solo primeros 5 para ejemplo
                'detected_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
                'fallback' => 'Usando URLs conocidas'
            ];
        }
    }
    
    /**
     * Obtiene el playlist M3U8 con reintentos y fallbacks
     */
    public function getPlaylistWithRetry($url = null, $maxRetries = 3) {
        $url = $url ?: $this->knownPlaylistUrl;
        
        for ($i = 0; $i < $maxRetries; $i++) {
            try {
                $context = stream_context_create([
                    'http' => [
                        'method' => 'GET',
                        'header' => [
                            'User-Agent: ' . $this->userAgent,
                            'Accept: application/vnd.apple.mpegurl,*/*',
                            'Referer: https://trivisioncr.com/',
                            'Origin: https://trivisioncr.com'
                        ],
                        'timeout' => 15
                    ]
                ]);
                
                $content = file_get_contents($url, false, $context);
                
                if ($content !== false && strpos($content, '#EXTM3U') !== false) {
                    return $content;
                }
                
            } catch (Exception $e) {
                error_log("Intento $i fallido: " . $e->getMessage());
            }
            
            // Esperar antes del siguiente intento
            if ($i < $maxRetries - 1) {
                sleep(2);
            }
        }
        
        return false;
    }
    
    /**
     * Genera un playlist M3U8 optimizado y autoactualizable
     */
    public function generateOptimizedPlaylist() {
        $content = $this->getPlaylistWithRetry();
        
        if (!$content) {
            return $this->generateFallbackPlaylist();
        }
        
        $lines = explode("\n", $content);
        $processedLines = [];
        $segmentCount = 0;
        $currentSequence = time();
        
        // Headers optimizados
        $processedLines[] = '#EXTM3U';
        $processedLines[] = '#EXT-X-VERSION:3';
        $processedLines[] = '#EXT-X-TARGETDURATION:10';
        $processedLines[] = '#EXT-X-MEDIA-SEQUENCE:' . $currentSequence;
        $processedLines[] = '#EXT-X-ALLOW-CACHE:NO';
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Saltar headers duplicados
            if (strpos($line, '#EXTM3U') === 0 || 
                strpos($line, '#EXT-X-VERSION') === 0 ||
                strpos($line, '#EXT-X-TARGETDURATION') === 0 ||
                strpos($line, '#EXT-X-MEDIA-SEQUENCE') === 0) {
                continue;
            }
            
            // Procesar líneas EXTINF
            if (strpos($line, '#EXTINF:') === 0) {
                $processedLines[] = $line;
            }
            // Procesar segmentos .ts
            elseif (preg_match('/\.ts(\?.*)?$/', $line)) {
                $filename = basename(parse_url($line, PHP_URL_PATH));
                
                // Usar proxy local para los segmentos
                $proxyUrl = $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . 
                           dirname($_SERVER['REQUEST_URI']) . '/segment_proxy.php?file=' . 
                           urlencode($filename);
                
                $processedLines[] = $proxyUrl;
                $segmentCount++;
            }
            // Mantener otras líneas importantes
            elseif (!empty($line) && $line[0] === '#') {
                $processedLines[] = $line;
            }
        }
        
        // Agregar información adicional
        $processedLines[] = '# Generated at: ' . date('Y-m-d H:i:s');
        $processedLines[] = '# Segments: ' . $segmentCount;
        
        return implode("\n", $processedLines);
    }
    
    /**
     * Genera un playlist de fallback cuando no se puede obtener el original
     */
    private function generateFallbackPlaylist() {
        return "#EXTM3U\n" .
               "#EXT-X-VERSION:3\n" .
               "#EXT-X-TARGETDURATION:10\n" .
               "#EXT-X-MEDIA-SEQUENCE:" . time() . "\n" .
               "#EXT-X-ALLOW-CACHE:NO\n" .
               "# Fallback playlist - Stream temporalmente no disponible\n" .
               "#EXT-X-ENDLIST\n";
    }
    
    /**
     * Obtiene estadísticas detalladas del stream
     */
    public function getDetailedStats() {
        $detectedUrls = $this->detectStreamUrls();
        $playlistContent = $this->getPlaylistWithRetry();
        
        $stats = [
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => $playlistContent ? 'online' : 'offline',
            'detected_urls' => $detectedUrls,
            'playlist_size' => $playlistContent ? strlen($playlistContent) : 0,
            'segments' => 0,
            'total_duration' => 0,
            'average_segment_duration' => 0
        ];
        
        if ($playlistContent) {
            $lines = explode("\n", $playlistContent);
            $durations = [];
            
            foreach ($lines as $line) {
                if (strpos($line, '#EXTINF:') === 0) {
                    $duration = floatval(str_replace(['#EXTINF:', ','], '', $line));
                    $durations[] = $duration;
                    $stats['total_duration'] += $duration;
                }
                if (preg_match('/\.ts(\?.*)?$/', $line)) {
                    $stats['segments']++;
                }
            }
            
            if (count($durations) > 0) {
                $stats['average_segment_duration'] = round(array_sum($durations) / count($durations), 2);
            }
        }
        
        return $stats;
    }
}

// Manejar la solicitud
$action = $_GET['action'] ?? 'playlist';
$extractor = new TrivisionAdvancedExtractor();

// Configurar headers apropiados
switch ($action) {
    case 'playlist':
        header('Content-Type: application/vnd.apple.mpegurl');
        header('Access-Control-Allow-Origin: *');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo $extractor->generateOptimizedPlaylist();
        break;
        
    case 'detect':
        header('Content-Type: application/json');
        echo json_encode($extractor->detectStreamUrls(), JSON_PRETTY_PRINT);
        break;
        
    case 'stats':
        header('Content-Type: application/json');
        echo json_encode($extractor->getDetailedStats(), JSON_PRETTY_PRINT);
        break;
        
    case 'test':
        header('Content-Type: text/html; charset=utf-8');
        echo "<!DOCTYPE html>
<html>
<head>
    <title>Trivision CR Stream Test</title>
    <meta charset='utf-8'>
</head>
<body>
    <h1>Trivision CR Stream Test</h1>
    <h2>Enlaces disponibles:</h2>
    <ul>
        <li><a href='?action=playlist'>Playlist M3U8</a></li>
        <li><a href='?action=detect'>Detectar URLs</a></li>
        <li><a href='?action=stats'>Estadísticas</a></li>
    </ul>
    
    <h2>Reproductor de prueba:</h2>
    <video width='640' height='360' controls>
        <source src='?action=playlist' type='application/vnd.apple.mpegurl'>
        Tu navegador no soporta HLS.
    </video>
    
    <script>
        // Recargar estadísticas cada 30 segundos
        setInterval(function() {
            fetch('?action=stats')
                .then(response => response.json())
                .then(data => console.log('Stats:', data));
        }, 30000);
    </script>
</body>
</html>";
        break;
        
    default:
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Acción no válida',
            'available_actions' => ['playlist', 'detect', 'stats', 'test']
        ]);
        break;
}
?>
