<?php
/**
 * Script para extraer M3U8 de Trivision CR con manejo de sesión
 * Primero accede a la página web para establecer sesión, luego busca el stream
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== EXTRACTOR M3U8 TRIVISION CR CON SESIÓN ===" . PHP_EOL . PHP_EOL;

// Crear un archivo temporal para cookies
$cookie_file = tempnam(sys_get_temp_dir(), 'trivision_cookies');

try {
    // Paso 1: Acceder a la página principal para establecer sesión
    echo "Paso 1: Estableciendo sesión con la página principal..." . PHP_EOL;
    $main_page = accessMainPage($cookie_file);
    
    if (!$main_page['success']) {
        throw new Exception("No se pudo acceder a la página principal: " . $main_page['error']);
    }
    
    echo "✓ Página principal accedida. Código HTTP: " . $main_page['http_code'] . PHP_EOL;
    echo "✓ Tamaño de respuesta: " . strlen($main_page['content']) . " bytes" . PHP_EOL;
    
    // Paso 2: Buscar URLs M3U8 en la página
    echo PHP_EOL . "Paso 2: Buscando URLs M3U8 en la página..." . PHP_EOL;
    $m3u8_urls = extractM3U8Urls($main_page['content']);
    
    if (empty($m3u8_urls)) {
        echo "❌ No se encontraron URLs M3U8 en la página" . PHP_EOL;
        echo "Intentando con URLs conocidas..." . PHP_EOL;
        $m3u8_urls = [
            'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8?DVR',
            'https://liveingesta318.cdnmedia.tv/trivisionlive/smil:rtmp01.smil/playlist.m3u8'
        ];
    } else {
        echo "✓ Encontradas " . count($m3u8_urls) . " URLs M3U8:" . PHP_EOL;
        foreach ($m3u8_urls as $url) {
            echo "  - $url" . PHP_EOL;
        }
    }
    
    // Paso 3: Probar cada URL M3U8 encontrada
    echo PHP_EOL . "Paso 3: Probando URLs M3U8..." . PHP_EOL;
    
    foreach ($m3u8_urls as $m3u8_url) {
        echo "Probando: $m3u8_url" . PHP_EOL;
        
        $playlist = getPlaylistWithSession($m3u8_url, $cookie_file);
        
        if ($playlist['success']) {
            echo "✓ Playlist obtenido exitosamente!" . PHP_EOL;
            echo "✓ Tamaño: " . strlen($playlist['content']) . " bytes" . PHP_EOL;
            
            // Verificar si es un M3U8 válido
            if (strpos($playlist['content'], '#EXTM3U') !== false) {
                echo "✓ Es un archivo M3U8 válido" . PHP_EOL;
                
                // Buscar chunklist
                if (preg_match('/chunklist.*\.m3u8/', $playlist['content'], $matches)) {
                    $chunklist_url = construirUrlChunklist($m3u8_url, $matches[0]);
                    echo "✓ Chunklist encontrado: $chunklist_url" . PHP_EOL;
                    
                    // Redirigir al chunklist
                    header("Location: " . $chunklist_url);
                    exit;
                } 
                // Si no hay chunklist, verificar segmentos .ts
                elseif (strpos($playlist['content'], '.ts') !== false) {
                    echo "✓ Playlist contiene segmentos .ts directos" . PHP_EOL;
                    header("Location: " . $m3u8_url);
                    exit;
                } else {
                    echo "ℹ Contenido del playlist:" . PHP_EOL;
                    echo $playlist['content'] . PHP_EOL;
                }
            } else {
                echo "❌ No es un archivo M3U8 válido" . PHP_EOL;
                echo "Contenido recibido:" . PHP_EOL;
                echo substr($playlist['content'], 0, 500) . PHP_EOL;
            }
        } else {
            echo "❌ Error al obtener playlist: " . $playlist['error'] . PHP_EOL;
            echo "❌ Código HTTP: " . $playlist['http_code'] . PHP_EOL;
        }
        
        echo "----------------------------------------" . PHP_EOL;
    }
    
    echo "❌ No se pudo obtener un stream funcional" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
} finally {
    // Limpiar archivo de cookies
    if (file_exists($cookie_file)) {
        unlink($cookie_file);
    }
}

// Función para acceder a la página principal
function accessMainPage($cookie_file) {
    $url = "https://trivisioncr.com/tv-en-vivo/";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_COOKIEJAR => $cookie_file,
        CURLOPT_COOKIEFILE => $cookie_file,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Cache-Control: max-age=0'
        ]
    ]);
    
    $content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => ($http_code == 200 && $content !== false),
        'content' => $content,
        'http_code' => $http_code,
        'error' => $error
    ];
}

// Función para extraer URLs M3U8 del HTML
function extractM3U8Urls($html) {
    $urls = [];
    
    $patterns = [
        '/https?:\/\/[^\s"\'<>]+\.m3u8(?:\?[^\s"\'<>]*)?/i',
        '/["\']([^"\']*liveingesta[^"\']*\.m3u8[^"\']*)["\']/',
        '/["\']([^"\']*trivision[^"\']*\.m3u8[^"\']*)["\']/',
        '/src\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']/',
        '/playlist\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']/'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match_all($pattern, $html, $matches)) {
            $found_urls = isset($matches[1]) ? $matches[1] : $matches[0];
            foreach ($found_urls as $url) {
                $clean_url = trim($url, '"\'');
                if (!in_array($clean_url, $urls)) {
                    $urls[] = $clean_url;
                }
            }
        }
    }
    
    return $urls;
}

// Función para obtener playlist con sesión establecida
function getPlaylistWithSession($url, $cookie_file) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_COOKIEFILE => $cookie_file,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: application/vnd.apple.mpegurl,*/*',
            'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Referer: https://trivisioncr.com/tv-en-vivo/',
            'Origin: https://trivisioncr.com'
        ]
    ]);
    
    $content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => ($http_code == 200 && $content !== false),
        'content' => $content,
        'http_code' => $http_code,
        'error' => $error
    ];
}

// Función para construir URL del chunklist
function construirUrlChunklist($m3u8_url, $chunklist) {
    $parsed_url = parse_url($m3u8_url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . dirname($parsed_url['path']) . '/';
    return $base_url . $chunklist;
}

echo PHP_EOL . "=== PROCESO COMPLETADO ===" . PHP_EOL;
?>
